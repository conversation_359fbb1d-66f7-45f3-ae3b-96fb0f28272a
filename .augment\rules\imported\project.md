---
type: "always_apply"
---

# 项目信息
- 项目名称：likeshop商城
- 描述：基于 ThinkPHP 5.1 的电商平台，支持多商户、多商品、多订单管理，集成多种第三方服务
- 版本：1.0.0
- 框架版本：ThinkPHP 5.1（LTS版本）
- PHP版本：>=7.0.0 <8.0.0（推荐使用PHP 7.2.9版本）

# 目录结构
```
www  WEB部署目录
├─application           应用目录
│  ├─common             公共模块目录
│  ├─module_name        模块目录
│  │  ├─common.php      模块函数文件
│  │  ├─controller      控制器目录
│  │  ├─model          模型目录
│  │  ├─view           视图目录
│  │  └─ ...           更多类库目录
│  │
│  ├─command.php        命令行定义文件
│  ├─common.php         公共函数文件
│  └─tags.php           应用行为扩展定义文件
│
├─config                应用配置目录
│  ├─module_name        模块配置目录
│  │  ├─database.php    数据库配置
│  │  ├─cache          缓存配置
│  │  └─ ...            
│  │
│  ├─app.php            应用配置
│  ├─cache.php          缓存配置
│  ├─cookie.php         Cookie配置
│  ├─database.php       数据库配置
│  ├─log.php            日志配置
│  ├─session.php        Session配置
│  ├─template.php       模板引擎配置
│  └─trace.php          Trace配置
│
├─route                 路由定义目录
│  ├─route.php          路由定义
│  └─...                更多
│
├─public                WEB目录（对外访问目录）
│  ├─index.php          入口文件
│  ├─router.php         快速测试文件
│  └─.htaccess          用于apache的重写
│
├─thinkphp              框架系统目录
│  ├─lang               语言文件目录
│  ├─library            框架类库目录
│  │  ├─think           Think类库包目录
│  │  └─traits          系统Trait目录
│  │
│  ├─tpl                系统模板目录
│  ├─base.php           基础定义文件
│  ├─console.php        控制台入口文件
│  ├─convention.php     框架惯例配置文件
│  ├─helper.php         助手函数文件
│  ├─phpunit.xml        phpunit配置文件
│  └─start.php          框架入口文件
│
├─extend                扩展类库目录
├─runtime               应用的运行时目录（可写，可定制）
├─vendor                第三方类库目录（Composer依赖库）
├─build.php             自动生成定义文件
├─composer.json         composer 定义文件
├─LICENSE.txt           授权说明文件
├─README.md             README 文件
└─think                 命令行入口文件
```

# 编码规范
- 使用 PSR-4 自动加载规范
- 类名使用 PascalCase 命名法
- 方法名和变量名使用 camelCase 命名法
- 常量名使用大写下划线命名法
- 代码缩进使用 4 个空格
- 每行代码不超过 120 个字符
- 文件末尾保留一个空行
- 目录和文件命名规范：
  * 目录不强制规范，驼峰和小写+下划线模式均支持
  * 类库、函数文件统一以 .php 为后缀
  * 类的文件名均以命名空间定义，并且命名空间的路径和类库文件所在路径一致
  * 类名和类文件名保持一致，统一采用驼峰法命名（首字母大写）
- 函数和类、属性命名规范：
  * 类的命名采用驼峰法，并且首字母大写，例如 `User`、`UserType`
  * 函数的命名使用小写字母和下划线（小写字母开头）的方式，例如 `get_client_ip`
  * 方法的命名使用驼峰法，并且首字母小写，例如 `getUserName`
  * 属性的命名使用驼峰法，并且首字母小写，例如 `tableName`、`instance`
  * 以双下划线"__"打头的函数或方法作为魔法方法，例如 `__call` 和 `__autoload`
- 常量和配置命名规范：
  * 常量以大写字母和下划线命名，例如 `APP_PATH` 和 `THINK_PATH`
  * 配置参数以小写字母和下划线命名，例如 `url_route_on` 和 `url_convert`
- 数据表和字段命名规范：
  * 数据表和字段采用小写加下划线方式命名
  * 字段名不要以下划线开头，例如 `think_user` 表和 `user_name` 字段
  * 不建议使用驼峰和中文作为数据表字段命名

# 技术栈
- 后端框架：PHP 7.0+ ~ PHP 7.x (不兼容PHP 8.0+) + ThinkPHP 5.1
- 数据库：MySQL（使用 utf8mb4 字符集）
- 缓存：Redis（通过配置支持）
- 消息队列：RabbitMQ（通过配置支持）
- 搜索引擎：Elasticsearch（通过配置支持）
- API 文档：Swagger（通过配置支持）
- 前端框架：Layui (后台)
- 第三方服务集成：
  * 微信 SDK (overtrue/wechat)
  * 阿里云 SDK (alibabacloud/client)
  * 七牛云 SDK (qiniu/php-sdk)
  * 阿里云 OSS (aliyuncs/oss-sdk-php)
  * 腾讯云 COS (qcloud/cos-sdk-v5)
  * 支付宝 SDK (alipaysdk/easysdk)
  * 腾讯云 SDK (tencentcloud/tencentcloud-sdk-php)
  * 图片处理 (topthink/think-image)
  * 验证码 (topthink/think-captcha)
  * 二维码生成 (endroid/qr-code)

# PHP版本兼容性注意事项
- 本项目必须在PHP 7.0.0及以上版本、PHP 8.0.0以下版本运行
- 在PHP 8.0及以上版本可能会遇到以下问题：
  * 函数参数顺序：PHP 8.0强制要求所有必选参数必须在可选参数之前定义
  * 数据类型处理：PHP 8.0对数据类型检查更加严格
  * 弃用功能：PHP 8.0移除了一些在PHP 7.x中弃用的功能
- 已知兼容性问题：
  * `auto_adapt`函数在PHP 8.0中会报错，因为必选参数`$fontfile`位于可选参数`$angle = 0`之后
  * 某些函数可能假设变量是特定类型（如数组），但未进行显式类型检查

# 开发流程
- 使用 Git Flow 工作流
- 遵循语义化版本规范
- 代码提交前必须通过 PHPUnit 单元测试
- 测试覆盖率要求达到 70% 以上
- 使用 PHP_CodeSniffer 进行代码风格检查
- 使用 PHPStan 进行静态代码分析
- 定期进行代码审查
- 使用 Travis CI 进行持续集成
- 遵循 ThinkPHP 5.1 的开发规范
- 使用 Composer 进行依赖管理
- 定期更新依赖包版本
- 保持代码注释的及时更新

# 错误与文档
- 使用 try...catch 语句捕获和处理异常
- 统一使用日志记录错误信息
- 使用 PHPDoc 编写函数和类注释
- 每个模块必须包含 README.md 文件
- API 接口必须包含详细的参数说明和返回值说明
- 数据库表结构必须包含字段说明
- 关键业务流程必须包含流程图说明
- 异常处理规范：
  * 业务异常使用自定义异常类
  * 系统异常统一记录日志
  * 敏感信息不直接返回给前端
- 日志记录规范：
  * 错误日志必须包含完整的堆栈信息
  * 业务日志需要包含关键参数
  * 日志分级：debug、info、warning、error
- 文档维护规范：
  * 及时更新接口文档
  * 保持文档与代码的一致性
  * 重要更新需要更新 CHANGELOG.md




