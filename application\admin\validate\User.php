<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\validate;
use think\Validate;

class User extends Validate{

    protected $rule = [
        'id'        => 'require',
        'nickname'  => 'require',
        'avatar'    => 'require',
        'password'  => 'length:6,16',
//        'mobile'    => "mobile|unique:user,mobile^del"
        'mobile'    => "mobile|checkMobile",
        'disable'   => 'require|in:0,1'
    ];

    protected $message = [
        'id.require'       => '请选择会员',
        'nickname.require' => '请输入会员昵称',
        'avatar.require'   => '请输入会员头像',
        'password'         => '密码长度为6~16位',
        'mobile.mobile'    => '请输入正确手机号',
        'mobile.unique'    => '手机号已被使用',
        'disable.require'  => '请选择禁用状态',
        'disable.in'       => '禁用状态参数错误',
    ];


    protected function checkMobile($value, $rule, $data)
    {
        $user = \app\admin\model\User::where([
            ['id', '<>', $data['id']],
            ['mobile', '=', $value],
            ['del', '=', 0]
        ])->find();

        if ($user) {
            return '手机号已被使用';
        }
        return true;
    }
}