{layout name="layout2" /}
<style>
    .layui-form-label {
        color: #6a6f6c;
    }
    .img-content{
        height:80px;
        line-height:80px
    }
    .img-container {
        float: left;
        opacity: 1;
        position: relative;
    }

    .img-src {
        width: 80px;
        height: 80px;
        padding: 4px;
    }
    .img-del-x {
        position: absolute;
        z-index: 100;
        top: -4px;
        right: -2px;
        width: 20px;
        height: 20px;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: hsla(0, 0%, 60%, .6);
        border-radius: 10px;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form-user_level" id="layuiadmin-form-user_level" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>专区名称：</label>
        <div class="layui-input-inline">
            <input type="text" name="name" lay-verify="required" lay-verType="tips" placeholder="请输入名称" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>专区标题：</label>
        <div class="layui-input-inline">
            <input type="text" name="title" lay-verify="required" lay-verType="tips" placeholder="请输入名称" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>专区图片：</label>
        <div class="layui-input-inline">
            <div class="img-content">
                <input name="image" type="hidden" value="">
                <div class="img-add"></div>
            </div>
        </div>
    </div>
    <div class="layui-form-item"><label class="layui-form-label"></label><span
            style="color: #a3a3a3;font-size: 9px">建议尺寸：300*436像素</span></div>
    <div class="layui-form-item">
        <label class="layui-form-label">专区状态：</label>
        <div class="layui-input-inline" id="" >
            <input type="radio" name="status" value=0 title="下架"  checked >
            <input type="radio" name="status" value=1 title="上架">
        </div>
    </div>
    <div class="layui-form-item"><label class="layui-form-label"></label><span
            style="color: #a3a3a3;font-size: 9px">专区下架表示隐藏专区入口，专区上架表示显示专区入口</span></div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="add-activity-submit" id="add-activity-submit" value="确认">
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like;
        //上传图片
        like.imageUpload('.img-add', function (uris, element) {
            if(uris.length>1){
                layer.msg('最多最能选中1张图片');
                return;
            }
            var html = '<div class="img-container">\n' +
                '<img class="img-src" ' +
                'src="' + uris[0] + '">' +
                '<a class="img-del-x">x</a>' +
                '</div>';
            element.prev().val(uris[0]);
            element.parent().append(html);
            element.css('display','none');
        }, true);
        //删除图片
        $(document).on('click', '.img-del-x', function () {
            $(this).parent().siblings('input').val('');
            $(this).parent().prev().css('display','block');
            $(this).parent().remove();
        });
        //显示图片
        $(document).on('click', '.img-src', function () {
            var image = $(this).attr('src');
            like.showImg(image,600);
        });



    })

</script>