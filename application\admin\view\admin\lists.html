{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">账号</label>
                    <div class="layui-input-block">
                        <input type="text" name="account" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">角色</label>
                    <div class="layui-input-block">
                        <select name="role_id">
                            <option value="">所有</option>
                            {volist name="role_lists" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layuiadmin-btn-admin {$view_theme_color}" lay-submit lay-filter="LAY-user-back-search">
                        <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layuiadmin-btn-admin {$view_theme_color} like-auth" auth-uri="admin/add" data-type="add">添加</button>
            </div>

            <table id="admin-lists" lay-filter="admin-lists"></table>
            <script type="text/html" id="login-state">
                {{#  if(d.login_state == 1){ }}
                <button class="layui-btn layui-btn-xs {$view_theme_button}">在线</button>
                {{#  } else { }}
                <button class="layui-btn layui-btn-xs layui-btn-primary">下线</button>
                {{#  } }}
            </script>
            <script type="text/html" id="buttonTpl">
                {{#  if(d.disable == 1){ }}
                <button class="layui-btn layui-btn-danger layui-btn-xs layui-bg-red">禁用</button>
                {{#  } else { }}
                <button class="layui-btn layui-btn-xs layui-btn-primary">启用</button>
                {{#  } }}
            </script>
            <script type="text/html" id="admin-operation">
                {{#  if(d.root == 1){ }}
                <a  class="layui-btn layui-btn-disabled layui-btn-xs"><i class="layui-icon layui-icon-edit like-auth" auth-uri="admin/edit"></i>编辑</a>
                <a class="layui-btn layui-btn-disabled layui-btn-xs"><i class="layui-icon layui-icon-delete"></i>删除</a>
                {{#  } else { }}
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
                {{#  } }}
            </script>
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like;

        //监听搜索
        form.on('submit(LAY-user-back-search)', function(data){
            var field = data.field;

            //执行重载
            table.reload('admin-lists', {
                where: field
            });
        });

        //事件
        var active = {
            batchdel: function(){
                var checkStatus = table.checkStatus('admin-lists')
                    ,checkData = checkStatus.data; //得到选中的数据

                if(checkData.length === 0){
                    return layer.msg('请选择数据');
                }

                layer.prompt({
                    formType: 1
                    ,title: '敏感操作，请验证口令，输ok'
                }, function(value, index){
                    layer.close(index);

                    layer.confirm('确定删除吗？', function(index) {

                        //执行 Ajax 后重载
                        /*
                        admin.req({
                          url: 'xxx'
                          //,……
                        });
                        */
                        table.reload('admin-lists');
                        layer.msg('已删除');
                    });
                });
            }
            ,add: function(){
                layer.open({
                    type: 2
                    ,title: '添加管理员'
                    ,content: '{:url("admin/add")}'
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'admin-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("admin/add")}',
                               data:field,
                               type:"post",
                               success:function(res)
                               {
                                   if(res.code == 1) {
                                       layui.layer.msg(res.msg, {
                                           offset: '15px'
                                           , icon: 1
                                           , time: 1000
                                       });
                                       layer.close(index); //关闭弹层
                                       table.reload('admin-lists'); //数据刷新
                                   }
                               }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }
        }
        $('.layui-btn.layuiadmin-btn-admin').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        //管理员管理
        table.render({
            elem: '#admin-lists'
            ,url: '{:url("admin/lists")}' //模拟接口
            ,cols: [[
                {field: 'id', width: 60, title: 'ID', sort: true}
                ,{field: 'account',width:80, title: '账号'}
                ,{field: 'name',width:80, title: '名称'}
                ,{field: 'role', width:120,title: '角色'}
                ,{field: 'create_time_str',width:170, title: '创建时间'}
                ,{field: 'login_time', width:170,title: '最后登录时间', sort: true}
                ,{field: 'login_ip', width:140,title: '最后登录ip',}
                ,{field: 'login_state',width:90, title:'登录状态', templet: '#login-state', minWidth: 40, align: 'center'}
                ,{field: 'disable', width:70,title:'状态', templet: '#buttonTpl', minWidth: 40, align: 'center'}
                ,{title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#admin-operation'}
                ,{title: '', width: '',align: 'center', fixed: 'right'}
            ]]
            ,text: {none: '暂无相关数据'}
            ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                return {
                    "code":res.code,
                    "msg":res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        //监听工具条
        table.on('tool(admin-lists)', function(obj){
            if(obj.event === 'del'){
                var id = obj.data.id;
                layer.prompt({
                    formType: 1
                    ,title: '删除操作，请输ok继续操作'
                }, function(value, index){
                    if(value!='ok'){
                        layer.close(index);
                        layer.msg('口令输入错误');
                        return;
                    }
                    layer.close(index);
                    layer.confirm('确定删除此管理员？', function(index){
                        like.ajax({
                            url:'{:url("admin/del")}',
                            data:{'admin_id':id},
                            type:"post",
                            success:function(res)
                            {
                                if(res.code == 1) {
                                    obj.del();
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    });
                                    layer.close(index);
                                }
                            }
                        });
                    });
                });
            }else if(obj.event === 'edit'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑管理员'
                    ,content: '{:url("admin/edit")}?admin_id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'admin-submit-edit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("admin/edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('admin-lists'); //数据刷新
                                    }
                                }
                            });
                        });

                        submit.trigger('click');
                    }
                })
            }
        });
    });
</script>