{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto;
    }
</style>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*虚拟评价。</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">
            <div class="layui-tab-item layui-show">
                <div class="layui-card">
                    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                        <div class="layui-form-item type">
                            <div class="layui-inline">
                                <label class="layui-form-label">商品名称:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="keyword" id="keyword" placeholder="请输入关键词"
                                           autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">商品分类:</label>
                                <div class="layui-input-block">
                                    <select name="category_id" id="category_id" placeholder="请选择商品分类">
                                        <option value="">全部</option>
                                        {foreach $category_list as $val }
                                        <option value="{$val.id}">{$val.html}{$val.name}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">商品状态:</label>
                                <div class="layui-input-block">
                                    <select name="status" id="status" placeholder="请选择商品状态">
                                        <option value="">全部</option>
                                        {foreach $status_list as $key => $val}
                                        <option value="{$key}">{$val}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-goods {$view_theme_color}"
                                        lay-submit lay-filter="goods-search">查询
                                </button>
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-goods layui-btn-primary "
                                        lay-submit lay-filter="goods-clear-search">清空查询
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table id="goods-lists" lay-filter="goods-lists"></table>
                        <script type="text/html" id="goods-info">
                            <img src="{{d.image}}" style="height:60px;width: 60px;margin-right: 5px;"
                                 class="image-show"> {{d.name}}
                        </script>
                        <script type="text/html" id="like-operation">
                            <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="add-comment">添加评论</a>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        version: "{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'table', 'like', 'form'], function () {
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , like = layui.like
            , element = layui.element;

        //监听搜索
        form.on('submit(goods-search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('goods-lists', {
                where: field,
                page: {curr: 1}
            });
        });

        //清空查询
        form.on('submit(goods-clear-search)', function () {
            $('#keyword').val('');  //清空输入框
            $('#category_id').val('');  //清空输入框
            $('#status').val('');  //清空输入框
            form.render('select');
            //刷新列表
            table.reload('goods-lists', {
                where: [],
                page: {curr: 1}
            });
        });


        $('.layui-btn.layuiadmin-btn-goods').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        //监听工具条
        table.on('tool(goods-lists)', function (obj) {
            var id = obj.data.id;

            if (obj.event === 'add-comment') {
                layer.open({
                    type: 2
                    , title: '添加评论'
                    , content: '{:url("CommentHelper/add")}?id=' + id
                    , area: ['90%', '90%']
                    , btn: ['确定', '取消']
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'add-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("CommentHelper/add")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index);
                                        table.reload('goods-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            }
        });

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src, 600);
        });

        var cols = [
            {field: 'name', title: '商品名称', width: 320, toolbar: '#goods-info'}
            , {field: 'cate_name', width: 160, title: '商品分类'}
            , {field: 'price', width: 180, title: '价格'}
            , {field: 'stock', width: 100, title: '总库存'}
            , {field: 'total_sales_sum', width: 100, title: '总销量'}
            , {field: 'status_desc', width: 100, align: 'center', title: '状态'}
            , {field: 'comment_count', width: 100, title: '评论数'}
            , {field: 'create_time_desc', width: 160, title: '发布时间'}
            , {fixed: 'right', title: '操作', align: 'center', width: 100, toolbar: '#like-operation'}
        ];
        table.render({
            id: 'goods-lists'
            , elem: '#goods-lists'
            , url: '{:url("CommentHelper/lists")}'
            , cols: [cols]
            , title: '商品表'
            , page: true
            , text: {none: '暂无数据！'}
            , parseData: function (res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.list, //解析数据列表
                };
            }
            , done: function (res, curr, count) {
                setTimeout(function () {
                    // 解决操作栏因为内容过多换行问题
                    $(".layui-table-fixed-l .layui-table-body").removeAttr("style");
                    $(".layui-table-fixed-r .layui-table-body").removeAttr("style");
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                }, 100)
            }
        });


    });
</script>