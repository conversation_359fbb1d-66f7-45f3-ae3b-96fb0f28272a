{layout name="layout2" /}
<style>
    html,
    body {
        background: #fff;
    }
    .layui-tab-title {
        background: #fff !important;
    }
    .layui-table-cell {
        height:auto;
    }
</style>
<div class="layui-fluid" style="margin-top:20px;">
    <div class="layui-card">
        <!-- 提示 -->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>* 分销会员申请列表，申请审核通过后即可成为分销会员；</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label for="keyword_type" class="layui-form-label">会员信息：</label>
                    <div class="layui-input-inline">
                        <select name="keyword_type" id="keyword_type">
                            <option value="sn">会员编号</option>
                            <option value="nickname">会员昵称</option>
                            <option value="mobile">手机号码</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <input type="text" name="keyword" id="keyword" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <a class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">搜索</a>
                    <a class="layui-btn layui-btn-sm layui-btn-primary" lay-submit lay-filter="clear-search">重置</a>
                </div>
            </div>
        </div>

        <!-- 主体 -->
        <div class="layui-tab layui-tab-card" lay-filter="like-tabs">
            <ul class="layui-tab-title">
                <li data-type="0" class="layui-this">待审核会员列表</li>
                <li data-type="1">审核通过会员列表</li>
                <li data-type="2">审核拒绝会员列表</li>
            </ul>
            <div class="layui-tab-content" style="padding: 0 15px;">
                <table id="like-table-lists" lay-filter="like-table-lists"></table>
                <script type="text/html" id="table-userInfo">
                    <img src="{{d.user.avatar}}" alt="头像" style="width:60px;height:60px;margin-right:5px;">
                    <div class="layui-inline" style="text-align:left;">
                        <p>会员编号：{{d.user.sn}}</p>
                        <p>会员昵称：{{d.user.nickname}}</p>
                        <p>会员等级：{{d.user.level.name || '未知'}}</p>
                    </div>
                </script>
                <script type="text/html" id="table-operation">
                    <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="detail">申请详情</a>
                    {{#  if(d.status === 0){ }}
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="audit">审核</a>
                    {{#  } }}
                </script>
            </div>
        </div>
    </div>
</div>


<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/modules/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(["table", "form", "element", "laydate", 'like'], function() {
        var table = layui.table;
        var form = layui.form;
        var like = layui.like;
        var element = layui.element;
        var laydate = layui.laydate;
        var $ = layui.jquery;

        like.tableLists("#like-table-lists", "{:url()}", [
            {field:"id", width:60, title:"ID"}
            ,{field:"user", width:250, title:"会员信息", templet:'#table-userInfo'}
            ,{field:"real_name", width:100, align:"center", title:"真实姓名"}
            // ,{field:"mobile", width:120, align:"center",title:"联系手机"}
            ,{field:"region", width:150, align:"center", title:"现住区域"}
            ,{field:"reason", width:150, align:"center", title:"申请原因"}
            ,{title:"操作", width:230, align:"center", fixed:"right", toolbar:"#table-operation"}
        ]);

        var active = {
            detail: function (obj) {
                layer.open({
                    type: 2
                    ,title: "申请详情"
                    ,content: "{:url('DistributionApply/detail')}?id=" + obj.data.id
                    ,area: ["600px", "600px"]

                });
            },
            audit: function (obj) {
                layer.open({
                    type: 2
                    ,title: "审核分销会员申请"
                    ,content: "{:url('DistributionApply/audit')}?id=" + obj.data.id
                    ,area: ["420px", "360px"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#addSubmit");
                        iframeWindow.layui.form.on("submit(addSubmit)", function(data){
                            data.field["id"] = obj.data.id;
                            like.ajax({
                                url: "{:url('DistributionApply/audit')}",
                                data: data.field,
                                type: "POST",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("like-table-lists", {
                                            where: {},
                                            page: { cur: 1 }
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger("click");
                    }
                });
            }
        };

        like.eventClick(active);

        element.on("tab(like-tabs)", function(){
            var type = this.getAttribute("data-type");
            table.reload("like-table-lists", {
                where: {type: type},
                page: { cur: 1 }
            });
        });

        /**
         * 搜索
         */
        form.on("submit(search)", function(data){
            data.field["type"] = $(".layui-tab-title li.layui-this").attr("lay-id");
            table.reload("like-table-lists", {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });

        /**
         * 清空搜索
         */
        form.on("submit(clear-search)", function(){
            var type = $(".layui-tab-title li.layui-this").attr("lay-id");
            $("#keyword_type").val("");
            $("#keyword").val("");
            form.render("select");
            table.reload("like-table-lists", {
                where: {type: type},
                page: {
                    curr: 1
                }
            });
        });
    });
</script>