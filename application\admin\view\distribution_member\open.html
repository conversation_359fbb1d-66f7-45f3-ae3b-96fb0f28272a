{layout name="layout2" /}
<style>
    /*.layui-form {*/
    /*    margin: 5px;*/
    /*}*/
    .layui-form-label {
        color: #6a6f6c !important;
    }
    .layui-input-block {
        width: 300px;
        line-height: 36px;
    }
    .layui-btn {
        margin-top: 5px;
    }
    select {
        width: 300px;
    }
    .layui-input {
        width: 300px;
    }
</style>
<form class="layui-form">
    <input type="hidden" name="user_id" id="user_id"  value="0">
    <div class="layui-form-item">
        <label class="layui-form-label">用户信息</label>
        <div class="layui-inline">
            <span id="user_selected"></span>
        </div>
        <div class="layui-inline">
            <button class="layui-btn layui-btn-sm layui-bg-blue" id="show-user">选择用户</button>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">分销等级</label>
        <div class="layui-input-block">
            <select name="level_id" id="level_id"  placeholder="请选择" >
                {foreach $levels as $val }
                <option value="{$val.id}">{$val.name}({$val.weights})级</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <div class="layui-input-block layui-hide">
            <button class="layui-btn" lay-submit lay-filter="openSubmit" id="openSubmit">立即提交</button>
        </div>
    </div>
</form>

<script>

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['element', 'form'], function () {
        var $ = layui.$
            , form = layui.form
            , layer = layui.layer
            , element = layui.element;

        $('#show-user').click(function() {
            layer.open({
                type: 2
                ,title: "选择用户"
                ,content: "{:url('distribution_member/userLists')}"
                ,area: ["90%", "90%"]
                ,btn: ["确定", "取消"]
                ,yes: function(index, layero){
                    var iframeWindow = window["layui-layer-iframe" + index];
                    let user_selected = iframeWindow.user_selected();
                    $('#user_selected').html(user_selected.nickname + '(' + user_selected.sn + ')');
                    $('#user_id').val(user_selected.id);
                    layer.close(index);
                }
            });
            return false;
        });
    });
</script>
